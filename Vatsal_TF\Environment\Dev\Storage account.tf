# Option 1: Single storage account in the first resource group
module "storageaccount" {
  source                   = "../../module/Storage_Account"
  storageaccountname       = var.storage_account_name
  rg_group                 = var.rg_group[0]  # Uses the first resource group: "Vatsal-RG"
  location                 = var.location
  account_tier             = var.account_tier
  account_replication_type = var.account_replication_type
  tags                     = var.tags
}

# Option 2: Uncomment below to create storage accounts in multiple resource groups
# module "storageaccount_multi" {
#   source                   = "../../module/Storage_Account"
#   for_each                 = toset(var.rg_group)
#   storageaccountname       = "${var.storage_account_name}${substr(each.value, -2, 2)}"  # Append RG suffix to make unique
#   rg_group                 = each.value
#   location                 = var.location
#   account_tier             = var.account_tier
#   account_replication_type = var.account_replication_type
#   tags                     = var.tags
# }