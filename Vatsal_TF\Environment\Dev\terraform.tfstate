{"version": 4, "terraform_version": "1.12.2", "serial": 3, "lineage": "740c6513-666d-e53d-bcee-105d754c15fd", "outputs": {}, "resources": [{"module": "module.rg_group", "mode": "managed", "type": "azurerm_resource_group", "name": "rg_group", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/b572f84c-ea09-465a-a35b-b4b1ee9a7152/resourceGroups/Vatsal-RG", "location": "westus2", "managed_by": "", "name": "Vatsal-RG", "tags": {}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.storageaccount", "mode": "managed", "type": "azurerm_storage_account", "name": "storageaccount", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 4, "attributes": {"access_tier": "Hot", "account_kind": "StorageV2", "account_replication_type": "GRS", "account_tier": "Standard", "allow_nested_items_to_be_public": true, "allowed_copy_scope": "", "azure_files_authentication": [], "blob_properties": [{"change_feed_enabled": false, "change_feed_retention_in_days": 0, "container_delete_retention_policy": [], "cors_rule": [], "default_service_version": "", "delete_retention_policy": [], "last_access_time_enabled": false, "restore_policy": [], "versioning_enabled": false}], "cross_tenant_replication_enabled": false, "custom_domain": [], "customer_managed_key": [], "default_to_oauth_authentication": false, "dns_endpoint_type": "Standard", "edge_zone": "", "https_traffic_only_enabled": true, "id": "/subscriptions/b572f84c-ea09-465a-a35b-b4b1ee9a7152/resourceGroups/Vatsal-RG/providers/Microsoft.Storage/storageAccounts/stvatsaldev", "identity": [], "immutability_policy": [], "infrastructure_encryption_enabled": false, "is_hns_enabled": false, "large_file_share_enabled": false, "local_user_enabled": true, "location": "westus2", "min_tls_version": "TLS1_2", "name": "stvatsaldev", "network_rules": [], "nfsv3_enabled": false, "primary_access_key": "****************************************************************************************", "primary_blob_connection_string": "DefaultEndpointsProtocol=https;BlobEndpoint=https://stvatsaldev.blob.core.windows.net/;AccountName=stvatsaldev;AccountKey=****************************************************************************************", "primary_blob_endpoint": "https://stvatsaldev.blob.core.windows.net/", "primary_blob_host": "stvatsaldev.blob.core.windows.net", "primary_blob_internet_endpoint": "", "primary_blob_internet_host": "", "primary_blob_microsoft_endpoint": "", "primary_blob_microsoft_host": "", "primary_connection_string": "DefaultEndpointsProtocol=https;AccountName=stvatsaldev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "primary_dfs_endpoint": "https://stvatsaldev.dfs.core.windows.net/", "primary_dfs_host": "stvatsaldev.dfs.core.windows.net", "primary_dfs_internet_endpoint": "", "primary_dfs_internet_host": "", "primary_dfs_microsoft_endpoint": "", "primary_dfs_microsoft_host": "", "primary_file_endpoint": "https://stvatsaldev.file.core.windows.net/", "primary_file_host": "stvatsaldev.file.core.windows.net", "primary_file_internet_endpoint": "", "primary_file_internet_host": "", "primary_file_microsoft_endpoint": "", "primary_file_microsoft_host": "", "primary_location": "westus2", "primary_queue_endpoint": "https://stvatsaldev.queue.core.windows.net/", "primary_queue_host": "stvatsaldev.queue.core.windows.net", "primary_queue_microsoft_endpoint": "", "primary_queue_microsoft_host": "", "primary_table_endpoint": "https://stvatsaldev.table.core.windows.net/", "primary_table_host": "stvatsaldev.table.core.windows.net", "primary_table_microsoft_endpoint": "", "primary_table_microsoft_host": "", "primary_web_endpoint": "https://stvatsaldev.z5.web.core.windows.net/", "primary_web_host": "stvatsaldev.z5.web.core.windows.net", "primary_web_internet_endpoint": "", "primary_web_internet_host": "", "primary_web_microsoft_endpoint": "", "primary_web_microsoft_host": "", "provisioned_billing_model_version": "", "public_network_access_enabled": true, "queue_encryption_key_type": "Service", "queue_properties": [{"cors_rule": [], "hour_metrics": [{"enabled": false, "include_apis": false, "retention_policy_days": 0, "version": "1.0"}], "logging": [{"delete": false, "read": false, "retention_policy_days": 0, "version": "1.0", "write": false}], "minute_metrics": [{"enabled": false, "include_apis": false, "retention_policy_days": 0, "version": "1.0"}]}], "resource_group_name": "Vatsal-RG", "routing": [], "sas_policy": [], "secondary_access_key": "****************************************************************************************", "secondary_blob_connection_string": "", "secondary_blob_endpoint": "", "secondary_blob_host": "", "secondary_blob_internet_endpoint": "", "secondary_blob_internet_host": "", "secondary_blob_microsoft_endpoint": "", "secondary_blob_microsoft_host": "", "secondary_connection_string": "DefaultEndpointsProtocol=https;AccountName=stvatsaldev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "secondary_dfs_endpoint": "", "secondary_dfs_host": "", "secondary_dfs_internet_endpoint": "", "secondary_dfs_internet_host": "", "secondary_dfs_microsoft_endpoint": "", "secondary_dfs_microsoft_host": "", "secondary_file_endpoint": "", "secondary_file_host": "", "secondary_file_internet_endpoint": "", "secondary_file_internet_host": "", "secondary_file_microsoft_endpoint": "", "secondary_file_microsoft_host": "", "secondary_location": "westcentralus", "secondary_queue_endpoint": "", "secondary_queue_host": "", "secondary_queue_microsoft_endpoint": "", "secondary_queue_microsoft_host": "", "secondary_table_endpoint": "", "secondary_table_host": "", "secondary_table_microsoft_endpoint": "", "secondary_table_microsoft_host": "", "secondary_web_endpoint": "", "secondary_web_host": "", "secondary_web_internet_endpoint": "", "secondary_web_internet_host": "", "secondary_web_microsoft_endpoint": "", "secondary_web_microsoft_host": "", "sftp_enabled": false, "share_properties": [{"cors_rule": [], "retention_policy": [{"days": 7}], "smb": []}], "shared_access_key_enabled": true, "static_website": [], "table_encryption_key_type": "Service", "tags": {"environment": "<PERSON>"}, "timeouts": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "primary_access_key"}], [{"type": "get_attr", "value": "primary_blob_connection_string"}], [{"type": "get_attr", "value": "primary_connection_string"}], [{"type": "get_attr", "value": "secondary_access_key"}], [{"type": "get_attr", "value": "secondary_blob_connection_string"}], [{"type": "get_attr", "value": "secondary_connection_string"}]], "identity_schema_version": 0, "identity": {"name": "stvatsaldev", "resource_group_name": "Vatsal-RG", "subscription_id": "b572f84c-ea09-465a-a35b-b4b1ee9a7152"}, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozNjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjozNjAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiI0In0="}]}], "check_results": null}