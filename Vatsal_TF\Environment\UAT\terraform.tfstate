{"version": 4, "terraform_version": "1.12.2", "serial": 1, "lineage": "0d28d54d-8a0a-0195-55b0-3db59156e8d2", "outputs": {}, "resources": [{"module": "module.rg_group", "mode": "managed", "type": "azurerm_resource_group", "name": "rg_group", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/b572f84c-ea09-465a-a35b-b4b1ee9a7152/resourceGroups/uat", "location": "westus2", "managed_by": "", "name": "uat", "tags": null, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo1NDAwMDAwMDAwMDAwLCJkZWxldGUiOjU0MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjo1NDAwMDAwMDAwMDAwfX0="}]}], "check_results": null}